/**
 * LLM-based widget generation utilities
 */

import type { LLMGenerationRequest, WidgetGenerationOptions } from "../types.js";
import { Logger } from "./logger.js";

export class LLMGenerator {
  private apiKey: string | undefined;
  private baseUrl: string;
  private model: string;

  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY || "sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy";
    this.baseUrl = process.env.OPENAI_BASE_URL || "http://***********:3000/v1";
    this.model = "volcengine/deepseek-v3";
  }

  /**
   * Generate custom widget using LLM
   */
  async generateCustomWidget(options: WidgetGenerationOptions): Promise<string> {
    if (!this.apiKey) {
      Logger.warning("OpenAI API key not found. Using fallback template generation.");
      return this.generateFallbackWidget(options);
    }

    try {
      const request: LLMGenerationRequest = {
        widgetType: options.type,
        widgetName: options.name,
        description: options.description,
        existingPatterns: this.getExistingPatterns(),
        targetPlacement: options.placement,
        targetSlot: options.slot,
        props: options.props,
      };

      const prompt = this.buildPrompt(request);
      const response = await this.callLLMAPI(prompt);

      return this.extractCodeFromResponse(response);
    } catch (error) {
      Logger.warning(`LLM generation failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      Logger.info("Falling back to template generation...");
      return this.generateFallbackWidget(options);
    }
  }

  /**
   * Build prompt for LLM
   */
  private buildPrompt(request: LLMGenerationRequest): string {
    return `You are an expert React developer creating a widget component for the CSCS Agent framework.

**Task**: Generate a React TypeScript component for a ${request.widgetType} widget named "${request.widgetName}".

**Requirements**:
1. Component name: ${request.widgetName}
2. Description: ${request.description || "A custom widget component"}
3. Target placement: ${request.targetPlacement}
4. Target slot: ${request.targetSlot || "blocks"}
5. Use TypeScript with proper interfaces
6. Follow React functional component patterns
7. Use Ant Design components for UI
8. Include proper error handling
9. Use CSCS Agent command system for interactions

**CSCS Agent Patterns**:
- Import useCommandRunner and BuildInCommand from "@cscs-agent/core"
- Use runner(BuildInCommand.SendMessage, {message: "text"}) to send messages
- Use runner(BuildInCommand.OpenSidePanel) to open side panel
- Use runner(BuildInCommand.InsertTextIntoSender, {text: "text"}) to insert text
- Use runner(BuildInCommand.InsertTagIntoSender, {text, rawValue, tooltips}) for tags
- Use runner(BuildInCommand.InsertSelectIntoSender, {placeholder, options, defaultValue}) for selects

**Existing Widget Patterns**:
${this.getExistingPatterns().join("\n")}

**Component Structure**:
1. Proper imports (React, Ant Design, CSCS Agent)
2. TypeScript interface for props
3. Functional component with proper typing
4. Event handlers using command runner
5. Proper JSX structure with Ant Design components
6. Export default statement

**Output**: Provide ONLY the complete React component code, no explanations or markdown formatting.

Generate the component now:`;
  }

  /**
   * Get existing widget patterns for context
   */
  private getExistingPatterns(): string[] {
    return [
      "// Button Widget Pattern:",
      "const ButtonWidget: React.FC<Props> = (props) => {",
      "  const runner = useCommandRunner();",
      "  const handleClick = () => runner(BuildInCommand.SendMessage, {message: 'clicked'});",
      "  return <Button onClick={handleClick}>{props.title}</Button>;",
      "};",
      "",
      "// Select Widget Pattern:",
      "const SelectWidget: React.FC<Props> = (props) => {",
      "  const [value, setValue] = useState();",
      "  const runner = useCommandRunner();",
      "  const onChange = (val) => {",
      "    setValue(val);",
      "    runner(BuildInCommand.SendMessage, {message: val});",
      "  };",
      "  return <Select options={props.options} onChange={onChange} />;",
      "};",
      "",
      "// Form Widget Pattern:",
      "const FormWidget: React.FC<Props> = (props) => {",
      "  const [form] = Form.useForm();",
      "  const runner = useCommandRunner();",
      "  const onFinish = (values) => {",
      "    runner(BuildInCommand.SendMessage, {message: JSON.stringify(values)});",
      "  };",
      "  return <Form form={form} onFinish={onFinish}>...</Form>;",
      "};",
    ];
  }

  /**
   * Call LLM API
   */
  private async callLLMAPI(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: "system",
            content:
              "You are an expert React TypeScript developer specializing in CSCS Agent widget components. Generate clean, production-ready code following the specified patterns.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 2000,
        temperature: 0.1,
      }),
    });

    if (!response.ok) {
      throw new Error(`LLM API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.choices || data.choices.length === 0) {
      throw new Error("No response from LLM API");
    }

    return data.choices[0].message.content;
  }

  /**
   * Extract code from LLM response
   */
  private extractCodeFromResponse(response: string): string {
    // Remove markdown code blocks if present
    let code = response.trim();

    // Remove ```typescript or ```tsx or ``` blocks
    code = code.replace(/^```(?:typescript|tsx|ts|javascript|jsx|js)?\n/, "");
    code = code.replace(/\n```$/, "");

    // Ensure the code starts with imports
    if (!code.includes("import")) {
      throw new Error("Generated code appears to be incomplete - no imports found");
    }

    // Ensure the code has an export default
    if (!code.includes("export default")) {
      throw new Error("Generated code appears to be incomplete - no export default found");
    }

    return code;
  }

  /**
   * Generate fallback widget when LLM is not available
   */
  private generateFallbackWidget(options: WidgetGenerationOptions): string {
    const componentName = options.name;
    const propsInterface = `${componentName}Props`;

    return `import React from "react";
import { Card, Typography, Button } from "antd";
import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";

const { Title, Text } = Typography;

interface ${propsInterface} {
  title?: string;
  description?: string;
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  const { title = "${componentName}", description = "${options.description || "A custom widget component"}" } = props;
  const runner = useCommandRunner();

  const handleAction = () => {
    runner(BuildInCommand.SendMessage, {
      message: \`Action triggered from \${title}\`,
    });
  };

  return (
    <Card size="small" style={{ margin: "8px 0" }}>
      <Title level={5}>{title}</Title>
      <Text>{description}</Text>
      <div style={{ marginTop: "12px" }}>
        <Button type="primary" onClick={handleAction}>
          Trigger Action
        </Button>
      </div>
    </Card>
  );
};

export default ${componentName};
`;
  }

  /**
   * Validate generated code
   */
  private validateGeneratedCode(code: string, componentName: string): boolean {
    // Basic validation checks
    const checks = [
      code.includes("import React"),
      code.includes(`const ${componentName}:`),
      code.includes("export default"),
      code.includes("React.FC"),
    ];

    return checks.every((check) => check);
  }
}
