/**
 * Project analysis utilities
 */

import { existsSync, readFileSync, readdirSync, statSync } from "fs";
import { join } from "path";

import type { PackageJson, ProjectStructure } from "../types.js";
import { Logger } from "./logger.js";

export class ProjectAnalyzer {
  /**
   * Analyze project structure
   */
  analyzeProject(projectPath: string): ProjectStructure {
    const srcDir = join(projectPath, "src");
    const agentConfigPath = join(srcDir, "agent-config.tsx");
    const agentConfigTsPath = join(srcDir, "agent-config.ts");
    const widgetsDir = join(srcDir, "widgets");
    const packageJsonPath = join(projectPath, "package.json");

    // Check for agent-config file (prefer .tsx over .ts)
    let hasAgentConfig = false;
    let finalAgentConfigPath = agentConfigPath;

    if (existsSync(agentConfigPath)) {
      hasAgentConfig = true;
      finalAgentConfigPath = agentConfigPath;
    } else if (existsSync(agentConfigTsPath)) {
      hasAgentConfig = true;
      finalAgentConfigPath = agentConfigTsPath;
    }

    return {
      hasAgentConfig,
      agentConfigPath: finalAgentConfigPath,
      widgetsDir,
      srcDir,
      packageJsonPath,
    };
  }

  /**
   * Validate project structure
   */
  validateProject(projectPath: string): { valid: boolean; error?: string } {
    // Check if directory exists
    if (!existsSync(projectPath)) {
      return { valid: false, error: `Project directory does not exist: ${projectPath}` };
    }

    if (!statSync(projectPath).isDirectory()) {
      return { valid: false, error: `Path is not a directory: ${projectPath}` };
    }

    const structure = this.analyzeProject(projectPath);

    // Check for package.json
    if (!existsSync(structure.packageJsonPath)) {
      return { valid: false, error: "No package.json found. This doesn't appear to be a valid project." };
    }

    // Check if it's a CSCS Agent project
    try {
      const packageJson: PackageJson = JSON.parse(readFileSync(structure.packageJsonPath, "utf8"));
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

      if (!dependencies["@cscs-agent/core"]) {
        return {
          valid: false,
          error: "This doesn't appear to be a CSCS Agent project. Missing @cscs-agent/core dependency.",
        };
      }
    } catch (error) {
      return { valid: false, error: "Invalid package.json file" };
    }

    // Check for src directory
    if (!existsSync(structure.srcDir)) {
      return { valid: false, error: "No src directory found" };
    }

    // Warn if no agent-config file exists
    if (!structure.hasAgentConfig) {
      Logger.warning("No agent-config.tsx or agent-config.ts file found. One will be created.");
    }

    return { valid: true };
  }

  /**
   * Check if widget already exists
   */
  widgetExists(projectPath: string, widgetName: string): boolean {
    const structure = this.analyzeProject(projectPath);
    const widgetPath = join(structure.widgetsDir, widgetName.toLowerCase());
    return existsSync(widgetPath);
  }

  /**
   * Get existing widgets in project
   */
  getExistingWidgets(projectPath: string): string[] {
    const structure = this.analyzeProject(projectPath);

    if (!existsSync(structure.widgetsDir)) {
      return [];
    }

    try {
      const items = readdirSync(structure.widgetsDir);
      return items.filter((item: string) => {
        const itemPath = join(structure.widgetsDir, item);
        return statSync(itemPath).isDirectory();
      });
    } catch (error) {
      Logger.warning(`Failed to read widgets directory: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    }
  }

  /**
   * Parse existing agent config to understand current widget registrations
   */
  parseAgentConfig(projectPath: string): { widgets: string[]; imports: string[] } {
    const structure = this.analyzeProject(projectPath);

    if (!structure.hasAgentConfig) {
      return { widgets: [], imports: [] };
    }

    try {
      const configContent = readFileSync(structure.agentConfigPath, "utf8");

      // Extract widget codes (simple regex parsing)
      const widgetMatches = configContent.match(/code:\s*["']([^"']+)["']/g) || [];
      const widgets = widgetMatches
        .map((match) => {
          const codeMatch = match.match(/["']([^"']+)["']/);
          return codeMatch ? codeMatch[1] : "";
        })
        .filter(Boolean);

      // Extract import statements
      const importMatches = configContent.match(/import\s+.*?from\s+["']([^"']+)["']/g) || [];
      const imports = importMatches
        .map((match) => {
          const importMatch = match.match(/from\s+["']([^"']+)["']/);
          return importMatch ? importMatch[1] : "";
        })
        .filter(Boolean);

      return { widgets, imports };
    } catch (error) {
      Logger.warning(`Failed to parse agent config: ${error instanceof Error ? error.message : "Unknown error"}`);
      return { widgets: [], imports: [] };
    }
  }

  /**
   * Get project information
   */
  getProjectInfo(projectPath: string): { name: string; version: string; description?: string } {
    const structure = this.analyzeProject(projectPath);

    try {
      const packageJson: PackageJson = JSON.parse(readFileSync(structure.packageJsonPath, "utf8"));
      return {
        name: packageJson.name,
        version: packageJson.version,
        description: packageJson.description,
      };
    } catch (error) {
      return {
        name: "unknown",
        version: "1.0.0",
      };
    }
  }
}
