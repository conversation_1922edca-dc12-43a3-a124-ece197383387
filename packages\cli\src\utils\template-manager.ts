/**
 * Template management utilities
 */

import { existsSync, readFileSync, readdirSync, statSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

// Import chalk after the class definition to avoid issues
import chalk from "chalk";

import type { PackageJson, TemplateMetadata, ValidationResult } from "../types.js";
import { Logger } from "./logger.js";

export class TemplateManager {
  private templatesDir: string;

  constructor() {
    // Get the CLI package directory
    const currentFile = fileURLToPath(import.meta.url);
    const cliDir = join(dirname(currentFile), "..", "..");
    this.templatesDir = join(cliDir, "templates");
  }

  /**
   * Get the templates directory path
   */
  getTemplatesDir(): string {
    return this.templatesDir;
  }

  /**
   * Get all available templates with metadata
   */
  getAvailableTemplates(): TemplateMetadata[] {
    if (!existsSync(this.templatesDir)) {
      Logger.error(`Templates directory not found: ${this.templatesDir}`);
      return [];
    }

    try {
      const templateDirs = readdirSync(this.templatesDir).filter((item) => {
        const itemPath = join(this.templatesDir, item);
        return statSync(itemPath).isDirectory();
      });

      return templateDirs.map((templateName) => {
        const templatePath = join(this.templatesDir, templateName);
        const packageJsonPath = join(templatePath, "package.json");

        let metadata: TemplateMetadata = {
          name: templateName,
          description: "",
          version: "1.0.0",
          valid: false,
          path: templatePath,
        };

        if (existsSync(packageJsonPath)) {
          try {
            const packageJson: PackageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));
            metadata = {
              name: templateName,
              description: packageJson.description || "",
              version: packageJson.version || "1.0.0",
              valid: true,
              path: templatePath,
            };
          } catch (error) {
            Logger.warning(
              `Failed to read package.json for template '${templateName}': ${error instanceof Error ? error.message : "Unknown error"}`,
            );
          }
        }

        return metadata;
      });
    } catch (error) {
      Logger.error(`Failed to read templates directory: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    }
  }

  /**
   * Validate a specific template
   */
  validateTemplate(templateName: string): ValidationResult {
    const templatePath = join(this.templatesDir, templateName);

    if (!existsSync(templatePath)) {
      return { valid: false, error: `Template '${templateName}' not found` };
    }

    if (!statSync(templatePath).isDirectory()) {
      return { valid: false, error: `Template '${templateName}' is not a directory` };
    }

    const packageJsonPath = join(templatePath, "package.json");
    if (!existsSync(packageJsonPath)) {
      return { valid: false, error: `Template '${templateName}' is missing package.json` };
    }

    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));
      if (!packageJson.name) {
        return { valid: false, error: `Template '${templateName}' package.json is missing name field` };
      }
    } catch (error) {
      return { valid: false, error: `Template '${templateName}' has invalid package.json` };
    }

    return { valid: true };
  }

  /**
   * Get template by name
   */
  getTemplate(templateName: string): TemplateMetadata | null {
    const templates = this.getAvailableTemplates();
    return templates.find((template) => template.name === templateName) || null;
  }

  /**
   * Check if template exists
   */
  templateExists(templateName: string): boolean {
    const templatePath = join(this.templatesDir, templateName);
    return existsSync(templatePath) && statSync(templatePath).isDirectory();
  }

  /**
   * Get default template name
   */
  getDefaultTemplate(): string {
    const templates = this.getAvailableTemplates();
    const validTemplates = templates.filter((t) => t.valid);

    // Look for 'basic' template first
    const basicTemplate = validTemplates.find((t) => t.name === "basic");
    if (basicTemplate) {
      return basicTemplate.name;
    }

    // Return first valid template
    if (validTemplates.length > 0) {
      return validTemplates[0].name;
    }

    return "basic"; // fallback
  }

  /**
   * List templates in a formatted way
   */
  listTemplates(): void {
    const templates = this.getAvailableTemplates();

    if (templates.length === 0) {
      Logger.warning("No templates found");
      return;
    }

    Logger.title("Available Templates");

    templates.forEach((template) => {
      const status = template.valid ? chalk.green("✓") : chalk.red("✗");
      const description = template.description ? ` - ${template.description}` : "";
      console.log(`  ${status} ${chalk.bold(template.name)}${description}`);
      if (template.version && template.valid) {
        console.log(`    ${chalk.dim(`Version: ${template.version}`)}`);
      }
    });

    Logger.newLine();
  }
}
